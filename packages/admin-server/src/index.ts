// First, set the env variables
import dotenv from "dotenv";
dotenv.config();

// Initialize Sentry before importing other modules
import { initSentry } from "./sentry";
initSentry();

import { clerkClient, clerkMiddleware, getAuth } from "@clerk/express";
import { Server } from "@hocuspocus/server";
import * as Sentry from "@sentry/node";
import {
  BasestationUpdateMessage,
  getActiveConfigurationForBasestation,
} from "@somo/shared";
import bodyParser from "body-parser";
import cors from "cors";
import express, { NextFunction, type Request, type Response } from "express";
import "express-async-errors";
import expressWinston from "express-winston";
import expressWs from "express-ws";
import ip from "ip";
import jwt from "jsonwebtoken";
import jwksClient from "jwks-rsa";
import { createRouteHandler } from "uploadthing/express";
import winston from "winston";
import { WebSocket } from "ws";
import * as Y from "yjs";
import { z } from "zod";
import { prisma } from "./prisma";
import { uploadRouter } from "./uploadthing";

const { app } = expressWs(express());

// parse request bodies
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

const port = Number.parseInt(process.env.PORT || "4000");

const ACTION_UPDATE = 0x01;
const ACTION_REBOOT = 0x02;

const hocuspocus = Server.configure({
  onAuthenticate: async (data) => {
    const client = jwksClient({
      jwksUri: process.env.CLERK_JWKS_URI ?? "",
    });

    const decoded = await new Promise<string | jwt.JwtPayload | undefined>(
      (resolve) => {
        jwt.verify(
          data.token,
          (header, callback) => {
            client.getSigningKey(header.kid, function (err, key) {
              if (err) {
                return callback(err);
              }
              if (!key) {
                return callback(new Error("Key not found"));
              }
              const signingKey = key.getPublicKey();
              callback(null, signingKey);
            });
          },
          {
            algorithms: ["RS256"],
          },
          (_error, decoded) => {
            return resolve(decoded);
          },
        );
      },
    );

    if (!decoded) {
      return false;
    }
    if (typeof decoded === "string") {
      return false;
    }
    if (!decoded.exp || !decoded.nbf) {
      return false;
    }
    const currentTime = Math.floor(Date.now() / 1000);
    if (decoded.exp < currentTime || decoded.nbf > currentTime) {
      return false;
    }
    return true;
  },
  onLoadDocument: async ({ documentName, document }) => {
    const now = Date.now();
    const orgId = documentName.split(":")[1];
    const roomId = documentName.split(":")[2];
    const room = await prisma.room.findFirst({
      where: { id: roomId, orgId },
    });

    if (room?.ydoc && room.ydoc.length > 0) {
      Y.applyUpdate(document, room.ydoc);
      const duration = Date.now() - now;
      console.log(
        `[hocuspocus] loaded document ${documentName} in ${duration}ms`,
      );
      return document;
    }
    return document;
  },
  onStoreDocument: async ({ documentName, document, context }) => {
    if (!context) {
      return document;
    }
    const now = Date.now();
    const data = Buffer.from(Y.encodeStateAsUpdate(document));

    const orgId = documentName.split(":")[1];
    const roomId = documentName.split(":")[2];

    await prisma.room.upsert({
      where: { id: roomId, orgId },
      update: { ydoc: data },
      create: { id: roomId, orgId, ydoc: data, name: roomId },
    });

    const duration = Date.now() - now;
    console.log(
      `[hocuspocus] stored document ${documentName} in ${duration}ms`,
    );
    return;
  },
});

// hocus pocus sync websocket
app.ws("/ws/sync", (ws, req) => {
  const connectionId = Math.random().toString(36).substring(7);
  const connectionStartTime = Date.now();
  const clientIp = req.socket.remoteAddress || "unknown";

  console.log(`[sync] New sync connection ${connectionId} from ${clientIp}`);

  try {
    const context = {};
    hocuspocus.handleConnection(ws, req, context);

    ws.on("close", (code, reason) => {
      const connectionDuration = Date.now() - connectionStartTime;
      console.log(`[sync] Sync connection ${connectionId} closed`, {
        code,
        reason: reason.toString(),
        clientIp,
        connectionDuration: `${connectionDuration}ms`,
        timestamp: new Date().toISOString(),
      });
    });

    ws.on("error", (error) => {
      console.error(`[sync] Sync connection ${connectionId} error:`, {
        error: error.message,
        stack: error.stack,
        clientIp,
        connectionDuration: Date.now() - connectionStartTime,
      });
    });
  } catch (error) {
    console.error(
      `[sync] Error handling sync connection ${connectionId}:`,
      error,
    );
  }
});

const wsClients: WebSocket[] = [];
const qrCodeSocketMap = new Map<string, WebSocket>();

async function sendControllers(ws: WebSocket) {
  try {
    const connectedControllers = await prisma.controller.findMany({
      where: {
        qrCode: {
          in: Array.from(qrCodeSocketMap.keys()),
        },
      },
    });

    ws.send(
      JSON.stringify({
        action: "controllers",
        controllers: connectedControllers.map((c) => ({
          ...c,
          lastSeen: c.lastSeen?.getTime() ?? new Date(),
        })),
      }),
    );
  } catch (error) {
    console.error("Error sending controllers:", error);
    // Don't crash the server, just log the error
  }
}

// service websocket for controller communication
app.ws("/ws/service", (ws, req) => {
  let qrCode = "";
  const connectionId = Math.random().toString(36).substring(7);
  const connectionStartTime = Date.now();
  const clientIp = req.socket.remoteAddress || "unknown";

  console.log(`[service] New connection ${connectionId} from ${clientIp}`);

  ws.on("message", async (message) => {
    try {
      const data = JSON.parse(message.toString());
      switch (data.action) {
        case "advertise": {
          qrCode = data.qrCode;
          qrCodeSocketMap.set(qrCode, ws);
          const ip = data.ip;
          const version = data.version;
          console.log(
            `[service] ${qrCode} connected (connection ${connectionId}) from ${clientIp}, version: ${version}`,
          );
          await prisma.controller.upsert({
            where: { qrCode: data.qrCode },
            update: { ip, lastSeen: new Date(), currentVersion: version },
            create: {
              qrCode: data.qrCode,
              ip,
              lastSeen: new Date(),
              currentVersion: version,
            },
          });
          // update all connected controllers
          for (const client of wsClients) {
            await sendControllers(client);
          }
          break;
        }
        case "update-confirmed": {
          const { qrCode, version } = z
            .object({
              qrCode: z.string(),
              version: z.string(),
            })
            .parse(data);
          await prisma.controller.update({
            where: { qrCode: qrCode },
            data: { currentVersion: version, nextVersion: null },
          });
          for (const client of wsClients) {
            await sendControllers(client);
          }
          break;
        }
        case "log": {
          const { message } = z
            .object({
              message: z.string(),
            })
            .parse(data);
          console.log(
            `[service] Log from ${qrCode || "unknown"} (connection ${connectionId}):`,
            message,
          );
          break;
        }
        default: {
          console.warn(
            `[service] Unknown action "${data?.action}" from ${qrCode || "unknown"} (connection ${connectionId})`,
            message,
          );
          break;
        }
      }
    } catch (error) {
      console.error(
        `[service] Error handling message from ${qrCode || "unknown"} (connection ${connectionId}):`,
        error,
      );
    }
  });

  ws.on("error", (error) => {
    console.error(
      `[service] WebSocket error for ${qrCode || "unknown"} (connection ${connectionId}):`,
      {
        error: error.message,
        stack: error.stack,
        clientIp,
        connectionDuration: Date.now() - connectionStartTime,
      },
    );
  });

  ws.on("close", async (code, reason) => {
    const connectionDuration = Date.now() - connectionStartTime;

    console.log(
      `[service] ${qrCode || "unknown"} disconnected (connection ${connectionId})`,
      {
        code,
        reason: reason.toString(),
        clientIp,
        connectionDuration: `${connectionDuration}ms`,
        timestamp: new Date().toISOString(),
      },
    );

    try {
      if (qrCode) {
        qrCodeSocketMap.delete(qrCode);
        for (const client of wsClients) {
          await sendControllers(client);
        }
      }
    } catch (error) {
      console.error("Error handling service WebSocket close:", error);
    }
  });
});

app.ws("/ws/controller", async (ws, _req) => {
  try {
    console.log("new controller ws connection");
    wsClients.push(ws);

    await sendControllers(ws);

    ws.on("message", async (message) => {
      try {
        const data = JSON.parse(message.toString());
        switch (data.action) {
          case "adopt": {
            const { roomId, orgId, qrCode } = z
              .object({
                roomId: z.string(),
                orgId: z.string(),
                qrCode: z.string(),
              })
              .parse(data);
            await prisma.controller.update({
              where: { qrCode },
              data: { roomId, orgId, currentVersion: null },
            });
            for (const client of wsClients) {
              await sendControllers(client);
            }
            break;
          }
          case "reboot": {
            const { qrCode } = z
              .object({
                qrCode: z.string(),
              })
              .parse(data);
            const controllerSocket = qrCodeSocketMap.get(qrCode);
            if (controllerSocket) {
              const data = Buffer.from([ACTION_REBOOT]);
              controllerSocket.send(data);
            }
            break;
          }
          case "update": {
            const {
              qrCode,
              version,
              nodes,
              edges,
              name,
              configuration,
              roomId,
            } = z
              .object({
                qrCode: z.string(),
                roomId: z.string(),
                version: z.string(),
                nodes: z.array(z.any()),
                edges: z.array(z.any()),
                name: z.string().prefault(""),
                configuration: z.object({
                  rf: z.object({
                    channel: z.number().prefault(15),
                    network: z.number().prefault(15),
                  }),
                  wifi: z.object({
                    ssid: z.string().prefault(""),
                    password: z.string().prefault(""),
                  }),
                  somoSwitchIdToQRCodeMapping: z
                    .array(
                      z.object({
                        id: z.string(),
                        qrCode: z.string(),
                      }),
                    )
                    .prefault([]),
                  somoDimmerIdToQRCodeMapping: z
                    .array(
                      z.object({
                        id: z.string(),
                        qrCode: z.string(),
                      }),
                    )
                    .prefault([]),
                  wiredSwitchIdToQRCodeMapping: z
                    .array(
                      z.object({
                        id: z.string(),
                        qrCode: z.string(),
                      }),
                    )
                    .prefault([]),
                  canBusControllerIdToQRCodeMapping: z
                    .array(
                      z.object({
                        id: z.string(),
                        qrCode: z.string(),
                      }),
                    )
                    .prefault([]),
                  doorSensorIdToQRCodeMapping: z
                    .array(
                      z.object({
                        id: z.string(),
                        qrCode: z.string(),
                      }),
                    )
                    .prefault([]),
                }),
              })
              .parse(data);
            const qrCodeSocket = qrCodeSocketMap.get(qrCode);
            await prisma.controller.upsert({
              where: { qrCode },
              update: {
                nextVersion: version,
                nodes,
                edges,
                name,
                configuration,
              },
              create: {
                qrCode,
                nextVersion: version,
                nodes,
                edges,
                name,
                configuration,
              },
            });
            for (const client of wsClients) {
              await sendControllers(client);
            }
            if (qrCodeSocket) {
              // Convert configuration QR mappings to nodeQrMappings format
              const nodeQrMappings = [
                ...(configuration.somoSwitchIdToQRCodeMapping || []),
                ...(configuration.somoDimmerIdToQRCodeMapping || []),
                ...(configuration.wiredSwitchIdToQRCodeMapping || []),
                ...(configuration.canBusControllerIdToQRCodeMapping || []),
                ...(configuration.doorSensorIdToQRCodeMapping || []),
              ].map((mapping) => ({
                deviceId: mapping.id,
                qrCode: mapping.qrCode,
              }));

              const { activeConfiguration } =
                getActiveConfigurationForBasestation({
                  name,
                  roomId,
                  version,
                  nodes,
                  nodeQrMappings,
                  rfConfig: configuration.rf,
                  wifiConfig: configuration.wifi,
                });

              const data = BasestationUpdateMessage.encode({
                qrCode,
                config: activeConfiguration.config,
              }).finish();

              // Prepend 0x01 to data
              const dataWithAction = Buffer.concat([
                Buffer.from([ACTION_UPDATE]),
                data,
              ]);

              console.log(
                "sending update to controller with len:",
                dataWithAction.length,
              );

              qrCodeSocket.send(dataWithAction);
            }
            break;
          }
        }
      } catch (error) {
        console.error("Error handling WebSocket message:", error);
        // Send error response to client if possible
        try {
          ws.send(
            JSON.stringify({
              action: "error",
              message: "Internal server error",
            }),
          );
        } catch (sendError) {
          console.error("Error sending error response:", sendError);
        }
      }
    });

    ws.on("close", async () => {
      console.log("controller ws connection closed");
      try {
        wsClients.splice(wsClients.indexOf(ws), 1);
      } catch (error) {
        console.error("Error handling WebSocket close:", error);
      }
    });
  } catch (error) {
    console.error("Error setting up WebSocket connection:", error);
  }
});

app.use(clerkMiddleware());

app.use(
  expressWinston.logger({
    transports: [new winston.transports.Console()],
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
    ),
    colorize: true,
  }),
);

app.use(
  cors({
    origin: "*",
  }),
);

const ensureUserIsInOrg = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const auth = getAuth(req);

  if (!auth.userId) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  try {
    const org = await clerkClient.organizations.getOrganizationMembershipList({
      organizationId: req.params.orgId,
    });

    const user = org.data.find((u) => u.publicUserData?.userId === auth.userId);
    if (!user || !user.publicUserData) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    const { userId, identifier, firstName, lastName } = user.publicUserData;
    Sentry.setUser({
      id: userId,
      email_address: identifier,
      username:
        firstName || lastName
          ? [firstName, lastName].filter(Boolean).join(" ")
          : identifier,
    });
  } catch (_error) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  return next();
};

app.get(
  "/org/:orgId/rooms",
  ensureUserIsInOrg,
  async (req: Request, res: Response) => {
    try {
      const rooms = await prisma.room.findMany({
        where: { orgId: req.params.orgId },
        select: {
          id: true,
          name: true,
          createdAt: true,
          updatedAt: true,
          readOnly: true,
        },
        orderBy: { createdAt: "desc" },
      });

      return res.json(rooms.map((room) => toResponse(room)));
    } catch (error) {
      console.error("Error fetching rooms:", error);
      return res.status(500).json({ message: "Internal server error" });
    }
  },
);

app.get(
  "/org/:orgId/rooms/:roomId",
  ensureUserIsInOrg,
  async (req: Request, res: Response) => {
    try {
      const room = await prisma.room.findUnique({
        where: { id: req.params.roomId, orgId: req.params.orgId },
        select: {
          id: true,
          name: true,
          createdAt: true,
          updatedAt: true,
          readOnly: true,
        },
      });

      if (!room) {
        return res.status(404).json({ message: "Room not found" });
      }

      return res.json(room);
    } catch (error) {
      console.error("Error fetching room:", error);
      return res.status(500).json({ message: "Internal server error" });
    }
  },
);

app.post(
  "/org/:orgId/rooms",
  ensureUserIsInOrg,
  async (req: Request, res: Response) => {
    try {
      const room = await prisma.room.create({
        data: {
          name: req.body.name,
          orgId: req.params.orgId,
          ydoc: new Uint8Array(),
        },
      });
      res.json(toResponse(room));
    } catch (error) {
      console.error("Error creating room:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  },
);

app.post(
  "/org/:orgId/rooms/copy",
  ensureUserIsInOrg,
  async (req: Request, res: Response) => {
    try {
      const { name, sourceRoomId } = req.body;
      const existingRoom = await prisma.room.findUnique({
        where: { id: sourceRoomId, orgId: req.params.orgId },
      });
      if (!existingRoom) {
        res.status(404).json({ message: "Room not found" });
        return;
      }

      const newRoom = await prisma.room.create({
        data: {
          name,
          orgId: req.params.orgId,
          ydoc: existingRoom.ydoc,
        },
      });
      res.json(toResponse(newRoom));
    } catch (error) {
      console.error("Error copying room:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  },
);

app.put(
  "/org/:orgId/rooms/:roomId",
  ensureUserIsInOrg,
  async (req: Request, res: Response) => {
    try {
      const room = await prisma.room.update({
        where: { id: req.params.roomId, orgId: req.params.orgId },
        data: { name: req.body.name, readOnly: req.body.readOnly },
      });
      res.json(toResponse(room));
    } catch (error) {
      console.error("Error updating room:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  },
);

function toResponse(data: { createdAt: Date; updatedAt: Date }) {
  return {
    ...data,
    // convert dates to unix timestamp
    createdAt: data.createdAt.getTime(),
    updatedAt: data.updatedAt.getTime(),
  };
}

app.delete(
  "/org/:orgId/rooms/:roomId",
  ensureUserIsInOrg,
  async (req: Request, res: Response) => {
    try {
      await prisma.room.delete({
        where: { id: req.params.roomId, orgId: req.params.orgId },
      });
      res.status(200).json({ message: "Room deleted" });
    } catch (error) {
      console.error("Error deleting room:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  },
);

app.use(
  "/api/upload",
  createRouteHandler({
    router: uploadRouter,
    config: {},
  }),
);

app.get("/health", async (_req: Request, res: Response) => {
  try {
    res.json({ status: "ok" });
  } catch (error) {
    console.error("Error in health check:", error);
    res.status(500).json({ message: "Internal server error" });
  }
});

// The error handler must be registered before any other error middleware and after all controllers
Sentry.setupExpressErrorHandler(app);

// Final error handler to prevent unhandled errors from crashing the server
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error(err.stack);

  // Handle specific error types if needed
  if (err instanceof SyntaxError) {
    return res.status(400).json({ message: "Invalid request syntax" });
  }

  // Handle validation errors
  if (err instanceof z.ZodError) {
    return res
      .status(400)
      .json({ message: "Validation error", errors: err.issues });
  }

  // Default error response
  return res.status(500).json({
    message:
      process.env.NODE_ENV === "production"
        ? "Internal server error"
        : err.message,
  });
});

app.listen(port, () => {
  console.log(`[server]: Server is running at http://${ip.address()}:${port}`);
});

// save all chats on SIGTERM
process.on("SIGTERM", () => {
  console.log("Received SIGTERM, closing HTTP server");
  prisma.$disconnect();
  process.exit(0);
});

process.on("SIGINT", () => {
  console.log("Received SIGINT, closing HTTP server");
  prisma.$disconnect();
  process.exit(0);
});

// Global error handlers to prevent crashes
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
  // Don't exit the process, just log the error
});

process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
  // Don't exit the process, just log the error
});
