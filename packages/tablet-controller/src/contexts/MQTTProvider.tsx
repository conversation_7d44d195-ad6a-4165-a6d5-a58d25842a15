import mqtt, { MqttClient } from "mqtt";
import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { z } from "zod";

interface MQTTContextType {
  // raw messages
  error: string | null;
  publish: (topic: string, message: string) => void;
  online: boolean;

  // somo device states
  deviceStates: DeviceStates;
  changeDeviceState: (state: DeviceState) => void;
}

const MQTTContext = createContext<MQTTContextType | undefined>(undefined);

interface MQTTProviderProps {
  children: React.ReactNode;
}

const deviceStateSchema = z.object({
  id: z.string(),
  brightness: z.number(),
  targetBrightness: z.number(),
  on: z.boolean(),
});

type DeviceState = z.infer<typeof deviceStateSchema>;
type DeviceStates = {
  [deviceId: string]: DeviceState;
};

const brokerUrl = "ws://somo-controller.local:9001";

export const MQTTProvider: React.FC<MQTTProviderProps> = ({ children }) => {
  const [error, setError] = useState<string | null>(null);
  const clientRef = useRef<MqttClient | null>(null);
  const topic = "somo/lights/#";
  const [deviceStates, setDeviceStates] = useState<DeviceStates>({});
  const [online, setOnline] = useState(false);

  useEffect(() => {
    if (import.meta.env.VITE_DEMO_MODE) {
      return;
    }
    const client = mqtt.connect(brokerUrl, {
      reconnectPeriod: 1000, // Reconnect every 1 second
    });

    clientRef.current = client;

    client.on("connect", () => {
      console.log("Connected to MQTT broker");
      setError(null);
      setOnline(true);
      client.subscribe(topic, (err) => {
        if (!err) {
          console.log(`Subscribed to topic: ${topic}`);
        } else {
          console.error("Subscription error:", err);
          setError("Subscription error: " + err.message);
        }
      });

      // get current device state
      client.publish(
        "somo/actions",
        JSON.stringify({ action: "getCurrentDeviceState" }),
      );
    });

    client.on("message", (topic, message) => {
      if (topic.startsWith("somo/lights")) {
        const rawJson = JSON.parse(message.toString());

        const result = deviceStateSchema.safeParse(rawJson);
        if (result.success) {
          setDeviceStates((prevStates) => {
            const found = prevStates[result.data.id];
            if (
              !found ||
              found.brightness !== result.data.brightness ||
              found.targetBrightness !== result.data.targetBrightness
            ) {
              return {
                ...prevStates,
                [result.data.id]: result.data,
              };
            }
            return prevStates;
          });
        } else {
          console.error("Invalid device state", result.error);
        }
      }
    });

    client.on("error", (err) => {
      console.error("MQTT Error:", err);
      setError("MQTT Error: " + err.message);
      setOnline(false);
    });

    client.on("reconnect", () => {
      console.log("Attempting to reconnect to MQTT broker...");
    });

    client.on("offline", () => {
      console.log("MQTT client is offline");
      setError("MQTT client is offline");
      setOnline(false);
    });

    return () => {
      client.end();
    };
  }, []);

  const publish = (topic: string, message: string) => {
    if (clientRef.current) {
      clientRef.current.publish(topic, message);
    }
  };

  const changeDeviceState = (state: DeviceState, dimSpeed = 0.3) => {
    if (clientRef.current) {
      setDeviceStates((prevStates) => ({
        ...prevStates,
        [state.id]: state,
      }));
      console.log("changeDeviceState", state);
      clientRef.current.publish(
        "somo/actions",
        JSON.stringify({
          action: "changeLight",
          id: state.id,
          on: state.targetBrightness > 0,
          brightness: state.targetBrightness,
          dimSpeed,
        }),
      );
    } else {
      console.error("MQTT client not connected");
    }
  };

  useEffect(() => {
    console.log("deviceStates", deviceStates);
  }, [deviceStates]);

  return (
    <MQTTContext.Provider
      value={{
        error,
        publish,
        deviceStates,
        changeDeviceState,
        online,
      }}
    >
      {children}
    </MQTTContext.Provider>
  );
};

export const useMQTT = () => {
  const context = useContext(MQTTContext);
  if (!context) {
    throw new Error("useMQTT must be used within an MQTTProvider");
  }
  return context;
};
