import { z } from "zod";
import { randomId } from "../../lib/randomId";

export const ThermostatControlSettings = z.object({
  id: z.string(),
  deviceId: z.string(),
  sortIndex: z.number(),
  setpoint: z.number(),
  mode: z.enum(["heat", "cool", "fan", "auto"] as const),
  fanSpeed: z.enum(["low", "medium", "high", "auto"] as const),
  type: z.literal("thermostat").prefault("thermostat"),
});
export type ThermostatControlSettings = z.infer<
  typeof ThermostatControlSettings
>;

export function aThermostatControlSettings(
  params: Partial<ThermostatControlSettings>,
): ThermostatControlSettings {
  return {
    id: `thermostat-action-settings-${randomId()}`,
    deviceId: `device-id-${randomId()}`,
    sortIndex: 0,
    setpoint: 22,
    mode: "auto",
    fanSpeed: "auto",
    type: "thermostat",
    ...params,
  };
}
