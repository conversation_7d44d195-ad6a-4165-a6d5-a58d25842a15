import { z } from "zod";
import { DeviceIconKey } from "../DeviceIcons";
import { BaseDevice } from "./BaseDevice";

export const servicePadViaIds = [
  "makeUpRoomButton",
  "doNotDisturbButton",
  "doorbellButton",
] as const;
export const ServicePadViaId = z.enum(servicePadViaIds);
export type ServicePadViaId = z.infer<typeof ServicePadViaId>;

export const ServicePadAction = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
});
export type ServicePadAction = z.infer<typeof ServicePadAction>;

export function aServicePadAction(
  params: Partial<ServicePadAction>,
): ServicePadAction {
  return {
    enabled: false,
    showLabel: false,
    name: "Service Pad Action",
    icon: undefined,
    ...params,
  };
}

export const ServicePadContainerNodeData = z.object({
  title: z.string(),
  mode: z
    .enum(["servicePad", "doorbell"])
    .describe("Service Pad or just Doorbell"),
  makeUpRoomButton: ServicePadAction,
  doNotDisturbButton: ServicePadAction,
  doorbellButton: ServicePadAction,
});
export type ServicePadContainerNodeData = z.infer<
  typeof ServicePadContainerNodeData
>;

export const ServicePadAnchorNodeData = z.record(z.string(), z.unknown());
export type ServicePadAnchorNodeData = z.infer<typeof ServicePadAnchorNodeData>;

export const ServicePadDevice = BaseDevice.extend({
  type: z.literal("servicePad"),
  nodeId: z.string(),
  viaId: ServicePadViaId,
});
export type ServicePadDevice = z.infer<typeof ServicePadDevice>;
