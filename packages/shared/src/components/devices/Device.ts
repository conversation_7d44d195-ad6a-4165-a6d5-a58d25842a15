import { z } from "zod";
import { <PERSON>ceIconKey } from "../DeviceIcons";
import { CurvePoint, CurveType } from "../DimmingCurve";
import { BaseDevice } from "./BaseDevice";
import {
  PirSensorDevice,
  RelayOutputDevice,
  ZeroToTenVoltDimmerDevice,
} from "./CanBusController";
import {
  MomentaryControllerDevice,
  ToggleControllerDevice,
} from "./Controller";
import { DmxDevice, DmxFixture } from "./Dmx";
import { DoorSensorDevice } from "./DoorSensor";
import { OutletDimmerDevice } from "./OutletDimmer";
import { PresenceSensorDevice } from "./PresenceSensor";
import { ServicePadDevice } from "./ServicePad";
import { SomoDimmerDevice } from "./SomoDimmer";
import { SomoFanDevice } from "./SomoFan";
import { SomoIrControllerDevice } from "./SomoIrController";
import { SomoShadesDevice } from "./SomoShades";
import { SomoSwitchDevice } from "./SomoSwitch";
import { SomoThermostatDevice } from "./SomoThermostat";
import { VirtualButtonDevice } from "./VirtualButton";

// We have some historical data that have no type and we need to preserve
export const HistoricalDevice = BaseDevice.extend({
  type: z.optional(z.undefined()),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  fixtures: z.record(z.string(), DmxFixture),
  isDimmable: z.boolean(),
  defaultDimmingSpeed: z.number(),
  dimmingCurve: z.array(CurvePoint).optional(),
  dimmingCurveType: CurveType.optional(),
});
export type HistoricalDevice = z.infer<typeof HistoricalDevice>;

export const Device = z.union([
  HistoricalDevice,
  DmxDevice,
  VirtualButtonDevice,
  MomentaryControllerDevice,
  ToggleControllerDevice,
  SomoSwitchDevice,
  SomoDimmerDevice,
  OutletDimmerDevice,
  PresenceSensorDevice,
  DoorSensorDevice,
  PirSensorDevice,
  ZeroToTenVoltDimmerDevice,
  RelayOutputDevice,
  SomoThermostatDevice,
  SomoIrControllerDevice,
  SomoShadesDevice,
  SomoFanDevice,
  ServicePadDevice,
]);
export type Device = z.infer<typeof Device>;
