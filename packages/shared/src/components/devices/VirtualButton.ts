import { z } from "zod";
import { DeviceIconKey } from "../DeviceIcons";
import { BaseDevice } from "./BaseDevice";
import { DeviceControlSettings } from "./DeviceControlSettings";

export const virtualButtonViaIds = ["via"] as const;
export const VirtualButtonViaId = z.enum(virtualButtonViaIds);
export type VirtualButtonViaId = z.infer<typeof VirtualButtonViaId>;

export const VirtualButtonVia = z.object({
  enabled: z.boolean(),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  lightName: z.string(),
  lightIcon: DeviceIconKey.optional(),
  showLabel: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type VirtualButtonVia = z.infer<typeof VirtualButtonVia>;

export const VirtualButtonContainerNodeData = z.object({
  title: z.string(),
  via: VirtualButtonVia,
});
export type VirtualButtonContainerNodeData = z.infer<
  typeof VirtualButtonContainerNodeData
>;

export const VirtualButtonAnchorNodeData = z.record(z.string(), z.unknown());
export type VirtualButtonAnchorNodeData = z.infer<
  typeof VirtualButtonAnchorNodeData
>;

export const VirtualButtonDevice = BaseDevice.extend({
  type: z.literal("virtualButton"),
  nodeId: z.string(),
  viaId: VirtualButtonViaId,
});
export type VirtualButtonDevice = z.infer<typeof VirtualButtonDevice>;
