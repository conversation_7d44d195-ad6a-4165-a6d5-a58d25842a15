import { z } from "zod";
import { randomId } from "../../lib/randomId";
import { DeviceIconKey } from "../DeviceIcons";
import { CurvePoint, CurveType } from "../DimmingCurve";
import { BaseDevice } from "./BaseDevice";

export const dmxFixtureTypes = [
  "Tunable White",
  "EST",
  "DF_12",
  "ELV",
  "D4",
  "Analog",
] as const;
export const DmxFixtureType = z.enum(dmxFixtureTypes);
export type DmxFixtureType = z.infer<typeof DmxFixtureType>;

export const DmxFixture = z.object({
  id: z.string(),
  sortIndex: z.number(),
  type: DmxFixtureType,
  channel: z.number(),
  defaultDimmingSpeed: z.number().optional(),
  minBrightness: z.number(),
  maxBrightness: z.number(),
});
export type DmxFixture = z.infer<typeof DmxFixture>;

export const DmxDevice = BaseDevice.extend({
  type: z.literal("dmx"),
  name: z.string(),
  icon: DeviceIconKey.optional(),
  fixtures: z.record(z.string(), DmxFixture),
  isDimmable: z.boolean(),
  defaultDimmingSpeed: z.number(),
  dimmingCurve: z.array(CurvePoint).optional(),
  dimmingCurveType: CurveType.optional(),
});
export type DmxDevice = z.infer<typeof DmxDevice>;

export function defaultDmxDevice(params: Partial<DmxDevice> = {}): DmxDevice {
  const defaultFixture = defaultDmxDeviceFixture();
  return aDmxDevice({
    id: randomId(),
    name: "Device",
    icon: "ceilingLamp",
    sortIndex: 0,
    defaultDimmingSpeed: 0.2,
    isDimmable: true,
    showLabel: true,
    dimmingCurveType: "linear",
    fixtures: {
      [defaultFixture.id]: defaultFixture,
    },
    ...params,
  });
}

export function aDmxDevice(params: Partial<DmxDevice>): DmxDevice {
  return {
    id: `dmx-device-${randomId()}`,
    type: "dmx",
    name: "DMX Device",
    sortIndex: 0,
    showLabel: false,
    defaultDimmingSpeed: 0,
    isDimmable: false,
    fixtures: {},
    ...params,
  };
}

export function defaultDmxDeviceFixture(
  params: Partial<DmxFixture> = {},
): DmxFixture {
  return aDmxFixture({
    id: randomId(),
    sortIndex: 0,
    type: "Analog",
    channel: 1,
    minBrightness: 0,
    maxBrightness: 100,
    defaultDimmingSpeed: 0.19,
    ...params,
  });
}

export function aDmxFixture(params: Partial<DmxFixture>): DmxFixture {
  return {
    id: `fixture-${randomId()}`,
    sortIndex: 0,
    type: "Tunable White",
    channel: 1,
    minBrightness: 0,
    maxBrightness: 100,
    ...params,
  };
}
