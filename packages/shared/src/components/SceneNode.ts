import { z } from "zod";
import { DeviceControlSettings } from "./devices";

export const sceneViaIds = ["onActivate", "onDeactivate"] as const;
export const SceneViaIds = z.enum(sceneViaIds);
export type SceneViaIds = z.infer<typeof SceneViaIds>;

export const SceneVia = z.object({
  enabled: z.boolean(),
  onUpClick: z.record(z.string(), DeviceControlSettings),
});
export type SceneVia = z.infer<typeof SceneVia>;

export const SceneNodeInput = z.object({
  source: z.string(),
  sourceHandle: z.string(),
});
export type SceneNodeInput = z.infer<typeof SceneNodeInput>;

export const SceneNodeData = z.object({
  title: z.string(),
  inputs: z.array(SceneNodeInput),
  onActivate: SceneVia,
  onDeactivate: SceneVia,
});
export type SceneNodeData = z.infer<typeof SceneNodeData>;
