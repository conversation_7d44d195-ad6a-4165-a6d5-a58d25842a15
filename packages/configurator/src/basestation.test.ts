import {
  aCanBusControllerContainerNode,
  aDeviceControlSettings,
  aDmxDevice,
  aDmxFixture,
  aSectionNode,
  aToggleCanBusController,
  Commands,
  GraphNode,
} from "@somo/shared";
import { describe, it } from "vitest";
import { MessageType } from "./contexts/BasestationEvents.js";
import {
  createBasestationData,
  getActiveConfig,
  sendCommandToDevices,
} from "./contexts/ExecutionContextSimulationProvider.js";
import { wait } from "./lib/async.js";
import { MainModule } from "./types/basestation";

const { default: createBasestationModule } = await import(
  // @ts-expect-error - TS can't find the file, but it exists or tests will fail
  "../public/basestation-wasm/common.js"
);

describe("Basestation", () => {
  let mainModule: MainModule;

  beforeAll(async () => {
    mainModule = await createBasestationModule();
  });

  it("can provision the basestation with basic configuration", () => {
    const nodes = [
      aSectionNode(
        { id: "section1" },
        {
          devices: {
            lamp1: aDmxDevice({
              id: "lamp1",
              fixtures: {
                fixture1: aDmxFixture({
                  channel: 3,
                  minBrightness: 15,
                  maxBrightness: 85,
                }),
              },
              defaultDimmingSpeed: 0.345,
            }),
          },
        },
      ),
      aCanBusControllerContainerNode(
        { id: "canbus1" },
        {
          controllers: {
            toggle1: aToggleCanBusController({
              portId: "3pin-port-1",
              onUpClick: {
                upClick1: aDeviceControlSettings({
                  deviceId: "lamp1",
                  dimSpeed: 1,
                  targetValue: 66,
                  onValue: 90,
                  offValue: 5,
                }),
              },
              onUpHold: {
                upHold1: aDeviceControlSettings({
                  deviceId: "lamp1",
                  dimSpeed: 0.2,
                  targetValue: 100,
                  onValue: 100,
                  offValue: 0,
                }),
              },
              onDownClick: {
                downClick1: aDeviceControlSettings({
                  deviceId: "lamp1",
                  dimSpeed: 0,
                  targetValue: 0,
                  onValue: 100,
                  offValue: 0,
                }),
              },
              onDownHold: {
                downHold1: aDeviceControlSettings({
                  deviceId: "lamp1",
                  dimSpeed: 0.4,
                  targetValue: 0,
                  onValue: 100,
                  offValue: 0,
                }),
              },
            }),
          },
        },
      ),
    ];

    provisionBasestation(nodes);

    const { config, state } = getActiveConfig(mainModule);
    expect(config).toMatchObject({
      canboConfigs: [
        {
          nodeId: 41,
          threePinInputs: [
            {
              toggle: {
                upClick: [1],
                upHold: [2],
                downClick: [3],
                downHold: [4],
              },
            },
          ],
        },
      ],
      actions: [
        {
          id: 1,
          lightId: 0,
          dimSpeedMsec: 1000,
          targetBrightness: 66,
          onBrightness: 90,
          offBrightness: 5,
        },
        {
          id: 2,
          lightId: 0,
          dimSpeedMsec: 200,
          targetBrightness: 100,
          onBrightness: 100,
          offBrightness: 0,
        },
        {
          id: 3,
          lightId: 0,
          dimSpeedMsec: 0,
          targetBrightness: 0,
          onBrightness: 100,
          offBrightness: 0,
        },
        {
          id: 4,
          lightId: 0,
          dimSpeedMsec: 400,
          targetBrightness: 0,
          onBrightness: 100,
          offBrightness: 0,
        },
      ],
      lights: [
        {
          id: 0,
          dimSpeedMsec: 345,
          fixtures: [
            {
              dmx: {
                channels: [3],
              },
              maxBrightness: 85,
              minBrightness: 15,
            },
          ],
        },
      ],
      nodeQrMappings: [
        {
          nodeId: 41,
          qrCode: "canbus1",
        },
      ],
    });
    expect(state?.lights).toMatchObject([
      {
        id: 0,
        dimSpeedMsec: 345,
        brightness: 0,
      },
    ]);
  });

  it("should update the state if we send a Toggle Up Click command", async () => {
    const nodes = [
      aSectionNode(
        { id: "section1" },
        {
          devices: {
            lamp1: aDmxDevice({
              id: "lamp1",
              fixtures: {
                fixture1: aDmxFixture({}),
              },
            }),
          },
        },
      ),
      aCanBusControllerContainerNode(
        { id: "canbus1" },
        {
          controllers: {
            toggle1: aToggleCanBusController({
              portId: "3pin-port-1",
              onUpClick: {
                upClick1: aDeviceControlSettings({
                  deviceId: "lamp1",
                  targetValue: 80,
                  dimSpeed: 0.1,
                }),
              },
            }),
          },
        },
      ),
    ];
    provisionBasestation(nodes);

    await sendToogleUpClickCommand({ containerId: "canbus1", connectorId: 1 });

    const { state } = getActiveConfig(mainModule);
    expect(state?.lights).toMatchObject([
      {
        id: 0,
        brightness: 80,
      },
    ]);
  });

  it("should update the state if we send a Toggle Up Hold command", async () => {
    const nodes = [
      aSectionNode(
        { id: "section1" },
        {
          devices: {
            lamp1: aDmxDevice({
              id: "lamp1",
              fixtures: {
                fixture1: aDmxFixture({}),
              },
            }),
          },
        },
      ),
      aCanBusControllerContainerNode(
        { id: "canbus1" },
        {
          controllers: {
            toggle1: aToggleCanBusController({
              portId: "3pin-port-1",
              onUpHold: {
                upHold1: aDeviceControlSettings({
                  deviceId: "lamp1",
                  targetValue: 100,
                  dimSpeed: 1,
                }),
              },
            }),
          },
        },
      ),
    ];
    provisionBasestation(nodes);

    await sendToogleUpHoldCommand({
      containerId: "canbus1",
      connectorId: 1,
      durationInMs: 500,
    });

    const { state } = getActiveConfig(mainModule);
    expect(state?.lights).toMatchObject([
      {
        id: 0,
        // Should be around 50, but give some flexibility
        brightness: expect.toSatisfy((value) => value > 48 && value < 53),
      },
    ]);
  });

  function provisionBasestation(nodes: GraphNode[]) {
    const { data } = createBasestationData(nodes);
    const success = mainModule.setActiveConfigurationFromProtobuf(data);
    expect(success, "Failed to provision the basestation").toBe(true);
  }

  async function sendToogleUpClickCommand({
    containerId,
    connectorId,
  }: {
    containerId: string;
    connectorId: number;
  }) {
    sendCommandToDevices(
      mainModule,
      containerId,
      MessageType.MESSAGE_CANBO_TOGGLE_BUTTON,
      Commands.ToggleButtonCommand.encode({
        connectorId,
        state: Commands.ToggleButtonCommand_State.Up,
      }).finish(),
    );
    sendCommandToDevices(
      mainModule,
      containerId,
      MessageType.MESSAGE_CANBO_TOGGLE_BUTTON,
      Commands.ToggleButtonCommand.encode({
        connectorId,
        state: Commands.ToggleButtonCommand_State.Released,
      }).finish(),
    );

    await wait(100);
    mainModule.processAllLights();
  }

  /**
   * `durationInMs` has to be at least 500ms, otherwise it's considered a click.
   * The helper will use a minimum duration of 500ms.
   */
  async function sendToogleUpHoldCommand({
    containerId,
    connectorId,
    durationInMs,
  }: {
    containerId: string;
    connectorId: number;
    durationInMs: number;
  }) {
    if (durationInMs < 500) {
      console.warn(
        `durationInMs is ${durationInMs}, but should be at least 500ms to be considered a hold. Using 500ms instead.`,
      );
      durationInMs = 500;
    }

    sendCommandToDevices(
      mainModule,
      containerId,
      MessageType.MESSAGE_CANBO_TOGGLE_BUTTON,
      Commands.ToggleButtonCommand.encode({
        connectorId,
        state: Commands.ToggleButtonCommand_State.Up,
      }).finish(),
    );

    await wait(durationInMs);
    mainModule.processAllLights();

    sendCommandToDevices(
      mainModule,
      containerId,
      MessageType.MESSAGE_CANBO_TOGGLE_BUTTON,
      Commands.ToggleButtonCommand.encode({
        connectorId,
        state: Commands.ToggleButtonCommand_State.Released,
      }).finish(),
    );

    await wait(100);
    mainModule.processAllLights();
  }
});
